/// Environment configuration for the application
enum Environment {
  development,
  staging,
  production,
}

/// Application configuration class
class AppConfig {
  static late Environment _environment;
  static bool _initialized = false;

  /// Initialize the app configuration with the specified environment
  static void initialize(Environment environment) {
    _environment = environment;
    _initialized = true;
  }

  /// Get the current environment
  static Environment get environment {
    if (!_initialized) {
      throw StateError('AppConfig not initialized. Call AppConfig.initialize() first.');
    }
    return _environment;
  }

  /// Check if the app configuration is initialized
  static bool get isInitialized => _initialized;

  /// Check if the app is running in development mode
  static bool get isDevelopment => environment == Environment.development;

  /// Check if the app is running in staging mode
  static bool get isStaging => environment == Environment.staging;

  /// Check if the app is running in production mode
  static bool get isProduction => environment == Environment.production;

  /// Get the app name based on environment
  static String get appName {
    switch (environment) {
      case Environment.development:
        return 'Bill Splitter (Dev)';
      case Environment.staging:
        return 'Bill Splitter (Staging)';
      case Environment.production:
        return 'Bill Splitter';
    }
  }

  /// Get debug mode flag
  static bool get debugMode {
    switch (environment) {
      case Environment.development:
      case Environment.staging:
        return true;
      case Environment.production:
        return false;
    }
  }
}
