import 'package:bill_splitter/domain/models/bill.dart';
import 'package:bill_splitter/domain/models/bill_item.dart';
import 'package:bill_splitter/domain/models/tip.dart';

/// Data model for bill storage
/// 
/// This class handles the conversion between domain models and storage format.
/// In this simple case, it delegates to the domain models' JSON methods,
/// but in more complex scenarios, it could handle data transformation,
/// versioning, and migration.
class BillDataModel {
  /// Private constructor to prevent instantiation
  BillDataModel._();

  /// Converts a domain Bill to storage format
  static Map<String, dynamic> toStorage(Bill bill) {
    return {
      'version': 1, // For future migration support
      'data': bill.toJson(),
    };
  }

  /// Converts storage format to domain Bill
  static Bill? fromStorage(Map<String, dynamic> storageData) {
    try {
      final version = storageData['version'] as int? ?? 1;
      final data = storageData['data'] as Map<String, dynamic>?;
      
      if (data == null) return null;

      // Handle different versions if needed in the future
      switch (version) {
        case 1:
          return Bill.fromJson(data);
        default:
          // Unknown version, try to parse as current version
          return Bill.fromJson(data);
      }
    } catch (e) {
      return null;
    }
  }

  /// Migrates old storage format to current version
  static Map<String, dynamic>? migrate(Map<String, dynamic> oldData) {
    try {
      final version = oldData['version'] as int? ?? 1;
      
      // No migration needed for version 1
      if (version == 1) return oldData;
      
      // Add migration logic here for future versions
      return oldData;
    } catch (e) {
      return null;
    }
  }
}

/// Data model for bill item storage
class BillItemDataModel {
  /// Private constructor to prevent instantiation
  BillItemDataModel._();

  /// Converts a domain BillItem to storage format
  static Map<String, dynamic> toStorage(BillItem item) {
    return item.toJson();
  }

  /// Converts storage format to domain BillItem
  static BillItem? fromStorage(Map<String, dynamic> data) {
    try {
      return BillItem.fromJson(data);
    } catch (e) {
      return null;
    }
  }
}

/// Data model for tip storage
class TipDataModel {
  /// Private constructor to prevent instantiation
  TipDataModel._();

  /// Converts a domain Tip to storage format
  static Map<String, dynamic> toStorage(Tip tip) {
    return tip.toJson();
  }

  /// Converts storage format to domain Tip
  static Tip? fromStorage(Map<String, dynamic> data) {
    try {
      return Tip.fromJson(data);
    } catch (e) {
      return null;
    }
  }
}
