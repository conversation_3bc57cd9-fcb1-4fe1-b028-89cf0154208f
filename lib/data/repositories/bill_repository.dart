import 'package:bill_splitter/domain/models/bill.dart';
import 'package:bill_splitter/data/services/local_storage_service.dart';
import 'package:bill_splitter/data/models/bill_data_model.dart';

/// Repository interface for bill data operations
/// 
/// This abstract class defines the contract for bill data persistence
/// and retrieval operations.
abstract class BillRepository {
  /// Saves the current bill to storage
  Future<bool> saveBill(Bill bill);

  /// Loads the current bill from storage
  /// Returns null if no bill is saved or if loading fails
  Future<Bill?> loadBill();

  /// Clears the saved bill from storage
  Future<bool> clearBill();

  /// Checks if a bill is currently saved
  Future<bool> hasSavedBill();
}

/// Implementation of BillRepository using local storage
class LocalBillRepository implements BillRepository {
  /// Creates a new LocalBillRepository
  /// 
  /// [storageService] - The local storage service to use for persistence
  const LocalBillRepository({
    required LocalStorageService storageService,
  }) : _storageService = storageService;

  final LocalStorageService _storageService;

  /// Storage key for the current bill
  static const String _billKey = 'current_bill';

  @override
  Future<bool> saveBill(Bill bill) async {
    try {
      final billData = BillDataModel.toStorage(bill);
      return await _storageService.saveJson(_billKey, billData);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Bill?> loadBill() async {
    try {
      final billData = await _storageService.loadJson(_billKey);
      if (billData == null) return null;

      return BillDataModel.fromStorage(billData);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> clearBill() async {
    try {
      return await _storageService.remove(_billKey);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> hasSavedBill() async {
    try {
      return await _storageService.containsKey(_billKey);
    } catch (e) {
      return false;
    }
  }
}
