import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling local storage operations
/// 
/// This service provides a clean interface for storing and retrieving
/// data using SharedPreferences.
abstract class LocalStorageService {
  /// Saves a JSON-serializable object to local storage
  Future<bool> saveJson(String key, Map<String, dynamic> data);

  /// Loads a JSON object from local storage
  Future<Map<String, dynamic>?> loadJson(String key);

  /// Removes data from local storage
  Future<bool> remove(String key);

  /// Clears all data from local storage
  Future<bool> clear();

  /// Checks if a key exists in local storage
  Future<bool> containsKey(String key);
}

/// Implementation of LocalStorageService using SharedPreferences
class SharedPreferencesStorageService implements LocalStorageService {
  SharedPreferencesStorageService._();

  static SharedPreferencesStorageService? _instance;
  static SharedPreferences? _prefs;

  /// Gets the singleton instance of the storage service
  static Future<SharedPreferencesStorageService> getInstance() async {
    if (_instance == null) {
      _instance = SharedPreferencesStorageService._();
      _prefs = await SharedPreferences.getInstance();
    }
    return _instance!;
  }

  @override
  Future<bool> saveJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = json.encode(data);
      return await _prefs!.setString(key, jsonString);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>?> loadJson(String key) async {
    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;
      
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> remove(String key) async {
    try {
      return await _prefs!.remove(key);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> clear() async {
    try {
      return await _prefs!.clear();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return _prefs!.containsKey(key);
    } catch (e) {
      return false;
    }
  }
}
