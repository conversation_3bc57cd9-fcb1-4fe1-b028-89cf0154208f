import 'package:flutter/foundation.dart';
import 'package:bill_splitter/domain/models/bill_item.dart';
import 'package:bill_splitter/domain/models/tip.dart';

/// Represents a complete bill with items and optional tip
/// 
/// This is an immutable value object that aggregates bill items
/// and calculates totals automatically.
@immutable
class Bill {
  /// Creates a new bill
  /// 
  /// [items] - List of bill items
  /// [tip] - Optional tip for the bill
  const Bill({
    required this.items,
    this.tip,
  });

  /// Creates an empty bill with one default item
  factory Bill.empty() {
    return const Bill(
      items: [BillItem.empty(order: 1)],
      tip: null,
    );
  }

  /// Creates a bill with default tip (10%)
  factory Bill.withDefaultTip(List<BillItem> items) {
    final subtotal = _calculateSubtotal(items);
    final tip = subtotal > 0 ? Tip.defaultTip(subtotal) : null;
    return Bill(
      items: items,
      tip: tip,
    );
  }

  /// List of bill items
  final List<BillItem> items;

  /// Optional tip for the bill
  final Tip? tip;

  /// Calculate subtotal from items
  static int _calculateSubtotal(List<BillItem> items) {
    return items.fold(0, (sum, item) => sum + item.total);
  }

  /// The subtotal of all items in cents
  int get subtotalInCents => _calculateSubtotal(items);

  /// The subtotal of all items in dollars
  double get subtotalInDollars => subtotalInCents / 100.0;

  /// The tip amount in cents (0 if no tip)
  int get tipAmountInCents => tip?.amountInCents ?? 0;

  /// The tip amount in dollars (0.0 if no tip)
  double get tipAmountInDollars => tipAmountInCents / 100.0;

  /// The total bill amount in cents (subtotal + tip)
  int get totalInCents => subtotalInCents + tipAmountInCents;

  /// The total bill amount in dollars (subtotal + tip)
  double get totalInDollars => totalInCents / 100.0;

  /// Whether the bill has any items with non-zero totals
  bool get hasValidItems => subtotalInCents > 0;

  /// Whether the bill has a tip
  bool get hasTip => tip != null;

  /// The number of items in the bill
  int get itemCount => items.length;

  /// Creates a copy of this bill with updated items
  Bill copyWithItems(List<BillItem> newItems) {
    // Reorder items to ensure sequential ordering
    final reorderedItems = newItems.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      return item.copyWith(order: index + 1);
    }).toList();

    // Update tip with new subtotal if tip exists
    final newSubtotal = _calculateSubtotal(reorderedItems);
    final updatedTip = tip?.copyWithSubtotal(newSubtotal);

    return Bill(
      items: reorderedItems,
      tip: updatedTip,
    );
  }

  /// Creates a copy of this bill with an updated tip
  Bill copyWithTip(Tip? newTip) {
    return Bill(
      items: items,
      tip: newTip,
    );
  }

  /// Creates a copy of this bill without a tip
  Bill copyWithoutTip() {
    return Bill(
      items: items,
      tip: null,
    );
  }

  /// Adds a new item to the bill
  Bill addItem() {
    final newOrder = items.length + 1;
    final newItem = BillItem.empty(order: newOrder);
    final newItems = [...items, newItem];
    return copyWithItems(newItems);
  }

  /// Removes an item from the bill by index
  Bill removeItem(int index) {
    if (index < 0 || index >= items.length || items.length <= 1) {
      return this; // Cannot remove if only one item or invalid index
    }
    
    final newItems = List<BillItem>.from(items);
    newItems.removeAt(index);
    return copyWithItems(newItems);
  }

  /// Updates an item in the bill
  Bill updateItem(int index, BillItem updatedItem) {
    if (index < 0 || index >= items.length) {
      return this; // Invalid index
    }

    final newItems = List<BillItem>.from(items);
    newItems[index] = updatedItem;
    return copyWithItems(newItems);
  }

  /// Adds a default tip to the bill
  Bill addDefaultTip() {
    if (subtotalInCents <= 0) return this;
    final newTip = Tip.defaultTip(subtotalInCents);
    return copyWithTip(newTip);
  }

  /// Updates the tip percentage
  Bill updateTipPercentage(int percentage) {
    if (subtotalInCents <= 0) return this;
    final newTip = Tip.fromPercentage(percentage, subtotalInCents);
    return copyWithTip(newTip);
  }

  /// Updates the tip amount in dollars
  Bill updateTipAmount(double amountInDollars) {
    if (subtotalInCents <= 0) return this;
    final newTip = Tip.fromAmountInDollars(amountInDollars, subtotalInCents);
    return copyWithTip(newTip);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bill &&
        listEquals(other.items, items) &&
        other.tip == tip;
  }

  @override
  int get hashCode {
    return Object.hash(Object.hashAll(items), tip);
  }

  @override
  String toString() {
    return 'Bill(items: ${items.length}, subtotal: \$${subtotalInDollars.toStringAsFixed(2)}, tip: ${tip?.toString() ?? 'none'}, total: \$${totalInDollars.toStringAsFixed(2)})';
  }

  /// Converts this bill to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'tip': tip?.toJson(),
    };
  }

  /// Creates a Bill from a JSON map
  factory Bill.fromJson(Map<String, dynamic> json) {
    final itemsJson = json['items'] as List<dynamic>;
    final items = itemsJson.map((itemJson) => BillItem.fromJson(itemJson as Map<String, dynamic>)).toList();
    
    final tipJson = json['tip'] as Map<String, dynamic>?;
    final tip = tipJson != null ? Tip.fromJson(tipJson) : null;

    return Bill(
      items: items,
      tip: tip,
    );
  }
}
