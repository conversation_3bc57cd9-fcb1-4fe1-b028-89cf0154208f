import 'package:flutter/foundation.dart';

/// Represents a single item in a bill
/// 
/// This is an immutable value object that represents a bill item with
/// automatic calculation of totals based on price and quantity.
@immutable
class BillItem {
  /// Creates a new bill item
  /// 
  /// [order] - The order/position of this item in the bill (1-based)
  /// [label] - The name/description of the item
  /// [price] - The unit price of the item in cents (to avoid floating point issues)
  /// [quantity] - The quantity of this item (must be positive)
  const BillItem({
    required this.order,
    required this.label,
    required this.price,
    required this.quantity,
  }) : assert(quantity > 0, 'Quantity must be positive');

  /// Creates a new bill item with default values
  /// 
  /// Used when adding a new item to the bill
  const BillItem.empty({required this.order})
      : label = '',
        price = 0,
        quantity = 1;

  /// The order/position of this item in the bill (1-based)
  final int order;

  /// The name/description of the item
  final String label;

  /// The unit price of the item in cents
  final int price;

  /// The quantity of this item
  final int quantity;

  /// The total price for this item (price * quantity)
  int get total => price * quantity;

  /// The unit price as a double in dollars
  double get priceInDollars => price / 100.0;

  /// The total price as a double in dollars
  double get totalInDollars => total / 100.0;

  /// Creates a copy of this item with updated values
  BillItem copyWith({
    int? order,
    String? label,
    int? price,
    int? quantity,
  }) {
    return BillItem(
      order: order ?? this.order,
      label: label ?? this.label,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
    );
  }

  /// Creates a copy with the price calculated from total and quantity
  BillItem copyWithTotalInCents(int totalInCents) {
    final newPrice = quantity > 0 ? (totalInCents / quantity).round() : 0;
    return copyWith(price: newPrice);
  }

  /// Creates a copy with the price calculated from total in dollars
  BillItem copyWithTotalInDollars(double totalInDollars) {
    final totalInCents = (totalInDollars * 100).round();
    return copyWithTotalInCents(totalInCents);
  }

  /// Creates a copy with the price set from dollars
  BillItem copyWithPriceInDollars(double priceInDollars) {
    final priceInCents = (priceInDollars * 100).round();
    return copyWith(price: priceInCents);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BillItem &&
        other.order == order &&
        other.label == label &&
        other.price == price &&
        other.quantity == quantity;
  }

  @override
  int get hashCode {
    return Object.hash(order, label, price, quantity);
  }

  @override
  String toString() {
    return 'BillItem(order: $order, label: $label, price: $price, quantity: $quantity, total: $total)';
  }

  /// Converts this item to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'order': order,
      'label': label,
      'price': price,
      'quantity': quantity,
    };
  }

  /// Creates a BillItem from a JSON map
  factory BillItem.fromJson(Map<String, dynamic> json) {
    return BillItem(
      order: json['order'] as int,
      label: json['label'] as String,
      price: json['price'] as int,
      quantity: json['quantity'] as int,
    );
  }
}
