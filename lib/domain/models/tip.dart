import 'package:flutter/foundation.dart';

/// Represents a tip for a bill
/// 
/// This is an immutable value object that can represent a tip either
/// as a percentage of the subtotal or as a fixed amount.
@immutable
class Tip {
  /// Creates a new tip
  /// 
  /// [percentage] - The tip percentage (0-100)
  /// [amountInCents] - The tip amount in cents
  /// [subtotalInCents] - The subtotal used for percentage calculations
  const Tip({
    required this.percentage,
    required this.amountInCents,
    required this.subtotalInCents,
  }) : assert(percentage >= 0, 'Percentage must be non-negative'),
       assert(amountInCents >= 0, 'Amount must be non-negative'),
       assert(subtotalInCents >= 0, 'Subtotal must be non-negative');

  /// Creates a tip with default values (10% of subtotal)
  factory Tip.defaultTip(int subtotalInCents) {
    const defaultPercentage = 10;
    final amountInCents = (subtotalInCents * defaultPercentage / 100).round();
    return Tip(
      percentage: defaultPercentage,
      amountInCents: amountInCents,
      subtotalInCents: subtotalInCents,
    );
  }

  /// Creates a tip from a percentage
  factory Tip.fromPercentage(int percentage, int subtotalInCents) {
    final amountInCents = (subtotalInCents * percentage / 100).round();
    return Tip(
      percentage: percentage,
      amountInCents: amountInCents,
      subtotalInCents: subtotalInCents,
    );
  }

  /// Creates a tip from an amount in cents
  factory Tip.fromAmountInCents(int amountInCents, int subtotalInCents) {
    final percentage = subtotalInCents > 0 
        ? (amountInCents * 100 / subtotalInCents).round()
        : 0;
    return Tip(
      percentage: percentage,
      amountInCents: amountInCents,
      subtotalInCents: subtotalInCents,
    );
  }

  /// Creates a tip from an amount in dollars
  factory Tip.fromAmountInDollars(double amountInDollars, int subtotalInCents) {
    final amountInCents = (amountInDollars * 100).round();
    return Tip.fromAmountInCents(amountInCents, subtotalInCents);
  }

  /// The tip percentage (0-100)
  final int percentage;

  /// The tip amount in cents
  final int amountInCents;

  /// The subtotal used for percentage calculations
  final int subtotalInCents;

  /// The tip amount as a double in dollars
  double get amountInDollars => amountInCents / 100.0;

  /// Creates a copy of this tip with updated values
  Tip copyWith({
    int? percentage,
    int? amountInCents,
    int? subtotalInCents,
  }) {
    return Tip(
      percentage: percentage ?? this.percentage,
      amountInCents: amountInCents ?? this.amountInCents,
      subtotalInCents: subtotalInCents ?? this.subtotalInCents,
    );
  }

  /// Creates a copy with updated subtotal and recalculated amount
  Tip copyWithSubtotal(int newSubtotalInCents) {
    final newAmountInCents = (newSubtotalInCents * percentage / 100).round();
    return Tip(
      percentage: percentage,
      amountInCents: newAmountInCents,
      subtotalInCents: newSubtotalInCents,
    );
  }

  /// Creates a copy with updated percentage and recalculated amount
  Tip copyWithPercentage(int newPercentage) {
    final newAmountInCents = (subtotalInCents * newPercentage / 100).round();
    return Tip(
      percentage: newPercentage,
      amountInCents: newAmountInCents,
      subtotalInCents: subtotalInCents,
    );
  }

  /// Creates a copy with updated amount and recalculated percentage
  Tip copyWithAmountInCents(int newAmountInCents) {
    final newPercentage = subtotalInCents > 0 
        ? (newAmountInCents * 100 / subtotalInCents).round()
        : 0;
    return Tip(
      percentage: newPercentage,
      amountInCents: newAmountInCents,
      subtotalInCents: subtotalInCents,
    );
  }

  /// Creates a copy with updated amount in dollars and recalculated percentage
  Tip copyWithAmountInDollars(double newAmountInDollars) {
    final newAmountInCents = (newAmountInDollars * 100).round();
    return copyWithAmountInCents(newAmountInCents);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tip &&
        other.percentage == percentage &&
        other.amountInCents == amountInCents &&
        other.subtotalInCents == subtotalInCents;
  }

  @override
  int get hashCode {
    return Object.hash(percentage, amountInCents, subtotalInCents);
  }

  @override
  String toString() {
    return 'Tip(percentage: $percentage%, amount: \$${amountInDollars.toStringAsFixed(2)}, subtotal: \$${(subtotalInCents / 100).toStringAsFixed(2)})';
  }

  /// Converts this tip to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'percentage': percentage,
      'amountInCents': amountInCents,
      'subtotalInCents': subtotalInCents,
    };
  }

  /// Creates a Tip from a JSON map
  factory Tip.fromJson(Map<String, dynamic> json) {
    return Tip(
      percentage: json['percentage'] as int,
      amountInCents: json['amountInCents'] as int,
      subtotalInCents: json['subtotalInCents'] as int,
    );
  }
}
