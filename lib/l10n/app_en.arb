{"@@locale": "en", "appTitle": "<PERSON>", "@appTitle": {"description": "The title of the application"}, "billEntryTitle": "Enter <PERSON>", "@billEntryTitle": {"description": "Title for the bill entry screen"}, "addItem": "Add Item", "@addItem": {"description": "Button text to add a new item"}, "deleteItem": "Delete Item", "@deleteItem": {"description": "Button text to delete an item"}, "itemLabel": "<PERSON><PERSON>", "@itemLabel": {"description": "Label for item name field"}, "price": "Price", "@price": {"description": "Label for price field"}, "quantity": "Quantity", "@quantity": {"description": "Label for quantity field"}, "total": "Total", "@total": {"description": "Label for total field"}, "tip": "Tip", "@tip": {"description": "Label for tip section"}, "tipPercentage": "Tip %", "@tipPercentage": {"description": "Label for tip percentage field"}, "tipAmount": "<PERSON><PERSON> Amount", "@tipAmount": {"description": "Label for tip amount field"}, "addTip": "Add Tip", "@addTip": {"description": "Button text to add tip"}, "deleteTip": "Delete Tip", "@deleteTip": {"description": "Button text to delete tip"}, "billTotal": "<PERSON>", "@billTotal": {"description": "Label for bill total"}, "continueButton": "Continue", "@continueButton": {"description": "Button text to continue to next screen"}, "errorEmptyBill": "Please add items with a total greater than $0.00", "@errorEmptyBill": {"description": "Error message when bill total is zero"}, "order": "Order", "@order": {"description": "Label for item order number"}}