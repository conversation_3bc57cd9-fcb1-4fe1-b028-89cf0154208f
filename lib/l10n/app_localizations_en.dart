// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Bill Splitter';

  @override
  String get billEntryTitle => 'Enter Bill Details';

  @override
  String get addItem => 'Add Item';

  @override
  String get deleteItem => 'Delete Item';

  @override
  String get itemLabel => 'Item';

  @override
  String get price => 'Price';

  @override
  String get quantity => 'Quantity';

  @override
  String get total => 'Total';

  @override
  String get tip => 'Tip';

  @override
  String get tipPercentage => 'Tip %';

  @override
  String get tipAmount => 'Tip Amount';

  @override
  String get addTip => 'Add Tip';

  @override
  String get deleteTip => 'Delete Tip';

  @override
  String get billTotal => 'Bill Total';

  @override
  String get continueButton => 'Continue';

  @override
  String get errorEmptyBill =>
      'Please add items with a total greater than \$0.00';

  @override
  String get order => 'Order';
}
