// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appTitle => 'Divisor de Conta';

  @override
  String get billEntryTitle => 'Inserir Detalhes da Conta';

  @override
  String get addItem => 'Adicionar Item';

  @override
  String get deleteItem => 'Excluir Item';

  @override
  String get itemLabel => 'Item';

  @override
  String get price => 'Preço';

  @override
  String get quantity => 'Quantidade';

  @override
  String get total => 'Total';

  @override
  String get tip => 'Gorjeta';

  @override
  String get tipPercentage => 'Gorjeta %';

  @override
  String get tipAmount => 'Valor da Gorjeta';

  @override
  String get addTip => 'Adicionar Gorjeta';

  @override
  String get deleteTip => 'Excluir Gorjeta';

  @override
  String get billTotal => 'Total da Conta';

  @override
  String get continueButton => 'Continuar';

  @override
  String get errorEmptyBill =>
      'Por favor, adicione itens com um total maior que R\$ 0,00';

  @override
  String get order => 'Ordem';
}
