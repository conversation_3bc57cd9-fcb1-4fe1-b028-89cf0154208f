import 'package:flutter/material.dart';
// import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:bill_splitter/config/app_config.dart';
import 'package:bill_splitter/ui/core/themes/app_theme.dart';
import 'package:bill_splitter/routing/app_router.dart';
import 'package:bill_splitter/data/services/local_storage_service.dart';
import 'package:bill_splitter/data/repositories/bill_repository.dart';
// import 'package:bill_splitter/ui/bill_entry/view_model/bill_entry_view_model.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void main() {
  // Initialize app configuration if not already done
  if (!AppConfig.isInitialized) {
    AppConfig.initialize(Environment.production);
  }
  runApp(const BillSplitterApp());
}

class BillSplitterApp extends StatelessWidget {
  const BillSplitterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SharedPreferencesStorageService>(
      future: SharedPreferencesStorageService.getInstance(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        final storageService = snapshot.data!;
        final billRepository = LocalBillRepository(storageService: storageService);

        return MultiProvider(
          providers: [
            Provider<LocalStorageService>.value(value: storageService),
            Provider<BillRepository>.value(value: billRepository),
            // ChangeNotifierProvider(
            //   create: (context) => BillEntryViewModel(
            //     billRepository: context.read<BillRepository>(),
            //   ),
            // ),
          ],
          child: MaterialApp.router(
            title: AppConfig.appName,
            debugShowCheckedModeBanner: AppConfig.debugMode,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
            // localizationsDelegates: const [
            //   AppLocalizations.delegate,
            //   GlobalMaterialLocalizations.delegate,
            //   GlobalWidgetsLocalizations.delegate,
            //   GlobalCupertinoLocalizations.delegate,
            // ],
            // supportedLocales: const [
            //   Locale('en', 'US'),
            //   Locale('pt', 'BR'),
            // ],
          ),
        );
      },
    );
  }
}


