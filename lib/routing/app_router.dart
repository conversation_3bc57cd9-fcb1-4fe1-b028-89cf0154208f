import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:bill_splitter/ui/bill_entry/widgets/bill_entry_screen.dart';

/// Application routing configuration
/// 
/// This class defines all the routes in the application using go_router.
class AppRouter {
  /// Private constructor to prevent instantiation
  AppRouter._();

  /// Route paths
  static const String billEntryPath = '/';
  static const String billSplitPath = '/split';

  /// Router configuration
  static final GoRouter router = GoRouter(
    initialLocation: billEntryPath,
    routes: [
      GoRoute(
        path: billEntryPath,
        name: 'billEntry',
        builder: (context, state) => const BillEntryScreen(),
      ),
      GoRoute(
        path: billSplitPath,
        name: 'billSplit',
        builder: (context, state) => const Scaffold(
          body: Center(
            child: Text('Bill Split Screen - Coming Soon'),
          ),
        ),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found: ${state.location}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(billEntryPath),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Extension on BuildContext for easy navigation
extension AppRouterExtension on BuildContext {
  /// Navigate to bill entry screen
  void goToBillEntry() => go(AppRouter.billEntryPath);

  /// Navigate to bill split screen
  void goToBillSplit() => go(AppRouter.billSplitPath);

  /// Navigate back
  void goBack() => pop();
}
