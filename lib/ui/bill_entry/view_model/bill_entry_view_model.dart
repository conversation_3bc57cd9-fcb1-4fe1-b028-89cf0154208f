import 'package:flutter/foundation.dart';
import 'package:bill_splitter/domain/models/bill.dart';
import 'package:bill_splitter/domain/models/bill_item.dart';
import 'package:bill_splitter/domain/models/tip.dart';
import 'package:bill_splitter/data/repositories/bill_repository.dart';
import 'package:bill_splitter/ui/core/ui/command.dart';

/// ViewModel for the bill entry screen
/// 
/// This ViewModel manages the state of the bill entry screen and provides
/// commands for user interactions following the MVVM pattern.
class BillEntryViewModel extends ChangeNotifier {
  /// Creates a new BillEntryViewModel
  /// 
  /// [billRepository] - Repository for bill data persistence
  BillEntryViewModel({
    required BillRepository billRepository,
  }) : _billRepository = billRepository {
    _initializeCommands();
    _loadBill();
  }

  final BillRepository _billRepository;

  Bill _bill = Bill.empty();
  bool _isLoading = false;
  String? _validationError;

  /// The current bill
  Bill get bill => _bill;

  /// Whether the ViewModel is loading
  bool get isLoading => _isLoading;

  /// Current validation error
  String? get validationError => _validationError;

  /// Whether the bill has validation errors
  bool get hasValidationError => _validationError != null;

  /// Whether the bill can proceed to the next step
  bool get canContinue => _bill.hasValidItems;

  // Commands
  late final AddItemCommand addItemCommand;
  late final DeleteItemCommand deleteItemCommand;
  late final UpdateItemCommand updateItemCommand;
  late final AddTipCommand addTipCommand;
  late final DeleteTipCommand deleteTipCommand;
  late final UpdateTipCommand updateTipCommand;
  late final ValidateAndContinueCommand validateAndContinueCommand;
  late final SaveBillCommand saveBillCommand;

  void _initializeCommands() {
    addItemCommand = AddItemCommand(this);
    deleteItemCommand = DeleteItemCommand(this);
    updateItemCommand = UpdateItemCommand(this);
    addTipCommand = AddTipCommand(this);
    deleteTipCommand = DeleteTipCommand(this);
    updateTipCommand = UpdateTipCommand(this);
    validateAndContinueCommand = ValidateAndContinueCommand(this);
    saveBillCommand = SaveBillCommand(this);
  }

  /// Loads the bill from storage
  Future<void> _loadBill() async {
    _setLoading(true);
    try {
      final savedBill = await _billRepository.loadBill();
      _bill = savedBill ?? Bill.empty();
      notifyListeners();
    } catch (e) {
      // If loading fails, start with empty bill
      _bill = Bill.empty();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// Updates the bill and notifies listeners
  void _updateBill(Bill newBill) {
    _bill = newBill;
    _clearValidationError();
    notifyListeners();
    // Auto-save the bill
    saveBillCommand.execute();
  }

  /// Sets the loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Sets a validation error
  void _setValidationError(String error) {
    _validationError = error;
    notifyListeners();
  }

  /// Clears the validation error
  void _clearValidationError() {
    if (_validationError != null) {
      _validationError = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    addItemCommand.dispose();
    deleteItemCommand.dispose();
    updateItemCommand.dispose();
    addTipCommand.dispose();
    deleteTipCommand.dispose();
    updateTipCommand.dispose();
    validateAndContinueCommand.dispose();
    saveBillCommand.dispose();
    super.dispose();
  }
}

/// Command to add a new item to the bill
class AddItemCommand extends SimpleCommand {
  AddItemCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(void parameter) async {
    final newBill = _viewModel._bill.addItem();
    _viewModel._updateBill(newBill);
  }
}

/// Command to delete an item from the bill
class DeleteItemCommand extends ParameterCommand<int> {
  DeleteItemCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(int? itemIndex) async {
    if (itemIndex == null) return;
    
    final newBill = _viewModel._bill.removeItem(itemIndex);
    _viewModel._updateBill(newBill);
  }
}

/// Parameters for updating an item
class UpdateItemParams {
  const UpdateItemParams({
    required this.itemIndex,
    required this.updatedItem,
  });

  final int itemIndex;
  final BillItem updatedItem;
}

/// Command to update an item in the bill
class UpdateItemCommand extends ParameterCommand<UpdateItemParams> {
  UpdateItemCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(UpdateItemParams? params) async {
    if (params == null) return;
    
    final newBill = _viewModel._bill.updateItem(params.itemIndex, params.updatedItem);
    _viewModel._updateBill(newBill);
  }
}

/// Command to add a tip to the bill
class AddTipCommand extends SimpleCommand {
  AddTipCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(void parameter) async {
    final newBill = _viewModel._bill.addDefaultTip();
    _viewModel._updateBill(newBill);
  }
}

/// Command to delete the tip from the bill
class DeleteTipCommand extends SimpleCommand {
  DeleteTipCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(void parameter) async {
    final newBill = _viewModel._bill.copyWithoutTip();
    _viewModel._updateBill(newBill);
  }
}

/// Parameters for updating tip
class UpdateTipParams {
  const UpdateTipParams.percentage(this.percentage) : amount = null;
  const UpdateTipParams.amount(this.amount) : percentage = null;

  final int? percentage;
  final double? amount;
}

/// Command to update the tip
class UpdateTipCommand extends ParameterCommand<UpdateTipParams> {
  UpdateTipCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(UpdateTipParams? params) async {
    if (params == null) return;
    
    Bill newBill;
    if (params.percentage != null) {
      newBill = _viewModel._bill.updateTipPercentage(params.percentage!);
    } else if (params.amount != null) {
      newBill = _viewModel._bill.updateTipAmount(params.amount!);
    } else {
      return;
    }
    
    _viewModel._updateBill(newBill);
  }
}

/// Command to validate the bill and continue to next screen
class ValidateAndContinueCommand extends SimpleCommand {
  ValidateAndContinueCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(void parameter) async {
    if (!_viewModel._bill.hasValidItems) {
      _viewModel._setValidationError('Please add items with a total greater than \$0.00');
      return;
    }
    
    _viewModel._clearValidationError();
    // Navigation will be handled by the UI layer
  }
}

/// Command to save the bill to storage
class SaveBillCommand extends SimpleCommand {
  SaveBillCommand(this._viewModel);

  final BillEntryViewModel _viewModel;

  @override
  Future<void> doExecute(void parameter) async {
    await _viewModel._billRepository.saveBill(_viewModel._bill);
  }
}
