import 'package:flutter/material.dart';
import 'package:bill_splitter/domain/models/bill_item.dart';
import 'package:bill_splitter/ui/core/ui/currency_input_field.dart';
import 'package:bill_splitter/ui/core/ui/integer_input_field.dart';
import 'package:bill_splitter/ui/core/ui/responsive_layout.dart';
import 'package:bill_splitter/ui/core/themes/app_theme.dart';
import 'package:bill_splitter/utils/currency_formatter.dart';

/// Widget that displays and allows editing of a single bill item
/// 
/// This widget provides input fields for all item properties and
/// handles the automatic calculations between price, quantity, and total.
class BillItemWidget extends StatefulWidget {
  /// Creates a BillItemWidget
  /// 
  /// [item] - The bill item to display and edit
  /// [itemIndex] - The index of this item in the bill
  /// [canDelete] - Whether the delete button should be shown
  /// [onItemUpdated] - Callback when the item is updated
  /// [onDeletePressed] - Callback when delete button is pressed
  const BillItemWidget({
    super.key,
    required this.item,
    required this.itemIndex,
    required this.canDelete,
    required this.onItemUpdated,
    required this.onDeletePressed,
  });

  final BillItem item;
  final int itemIndex;
  final bool canDelete;
  final ValueChanged<BillItem> onItemUpdated;
  final VoidCallback onDeletePressed;

  @override
  State<BillItemWidget> createState() => _BillItemWidgetState();
}

class _BillItemWidgetState extends State<BillItemWidget> {
  late TextEditingController _labelController;
  late FocusNode _labelFocusNode;

  @override
  void initState() {
    super.initState();
    _labelController = TextEditingController(text: widget.item.label);
    _labelFocusNode = FocusNode();

    // Auto-focus label field for new items
    if (widget.item.label.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _labelFocusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(BillItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.item.label != widget.item.label) {
      _labelController.text = widget.item.label;
    }
  }

  @override
  void dispose() {
    _labelController.dispose();
    _labelFocusNode.dispose();
    super.dispose();
  }

  void _updateItem(BillItem updatedItem) {
    widget.onItemUpdated(updatedItem);
  }

  void _onLabelChanged(String label) {
    _updateItem(widget.item.copyWith(label: label));
  }

  void _onPriceChanged(double price) {
    _updateItem(widget.item.copyWithPriceInDollars(price));
  }

  void _onQuantityChanged(int quantity) {
    _updateItem(widget.item.copyWith(quantity: quantity));
  }

  void _onTotalChanged(double total) {
    _updateItem(widget.item.copyWithTotalInDollars(total));
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppThemeExtension.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with order number and delete button
            Row(
              children: [
                Text(
                  'Item ${widget.item.order}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                if (widget.canDelete)
                  IconButton(
                    onPressed: widget.onDeletePressed,
                    icon: const Icon(Icons.delete_outline),
                    tooltip: 'Delete Item',
                  ),
              ],
            ),
            
            const SizedBox(height: AppThemeExtension.spacingM),
            
            // Input fields
            ResponsiveLayout(
              mobile: _buildMobileLayout(),
              tablet: _buildTabletLayout(),
              desktop: _buildDesktopLayout(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        // Label field (full width)
        TextField(
          controller: _labelController,
          focusNode: _labelFocusNode,
          decoration: const InputDecoration(
            labelText: 'Item Name',
            hintText: 'Enter item name',
          ),
          onChanged: _onLabelChanged,
        ),
        
        const SizedBox(height: AppThemeExtension.spacingM),
        
        // Price and Quantity (side by side)
        Row(
          children: [
            Expanded(
              child: CurrencyInputField(
                label: 'Price',
                value: widget.item.priceInDollars,
                onChanged: _onPriceChanged,
              ),
            ),
            const SizedBox(width: AppThemeExtension.spacingM),
            Expanded(
              child: IntegerInputField(
                label: 'Quantity',
                value: widget.item.quantity,
                min: 1,
                onChanged: _onQuantityChanged,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppThemeExtension.spacingM),
        
        // Total (full width)
        CurrencyInputField(
          label: 'Total',
          value: widget.item.totalInDollars,
          onChanged: _onTotalChanged,
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Column(
      children: [
        // Label field (full width)
        TextField(
          controller: _labelController,
          focusNode: _labelFocusNode,
          decoration: const InputDecoration(
            labelText: 'Item Name',
            hintText: 'Enter item name',
          ),
          onChanged: _onLabelChanged,
        ),
        
        const SizedBox(height: AppThemeExtension.spacingM),
        
        // Price, Quantity, and Total (side by side)
        Row(
          children: [
            Expanded(
              child: CurrencyInputField(
                label: 'Price',
                value: widget.item.priceInDollars,
                onChanged: _onPriceChanged,
              ),
            ),
            const SizedBox(width: AppThemeExtension.spacingM),
            Expanded(
              child: IntegerInputField(
                label: 'Quantity',
                value: widget.item.quantity,
                min: 1,
                onChanged: _onQuantityChanged,
              ),
            ),
            const SizedBox(width: AppThemeExtension.spacingM),
            Expanded(
              child: CurrencyInputField(
                label: 'Total',
                value: widget.item.totalInDollars,
                onChanged: _onTotalChanged,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Label field (takes more space)
        Expanded(
          flex: 3,
          child: TextField(
            controller: _labelController,
            focusNode: _labelFocusNode,
            decoration: const InputDecoration(
              labelText: 'Item Name',
              hintText: 'Enter item name',
            ),
            onChanged: _onLabelChanged,
          ),
        ),
        
        const SizedBox(width: AppThemeExtension.spacingM),
        
        // Price
        Expanded(
          flex: 2,
          child: CurrencyInputField(
            label: 'Price',
            value: widget.item.priceInDollars,
            onChanged: _onPriceChanged,
          ),
        ),
        
        const SizedBox(width: AppThemeExtension.spacingM),
        
        // Quantity
        Expanded(
          flex: 1,
          child: IntegerInputField(
            label: 'Quantity',
            value: widget.item.quantity,
            min: 1,
            onChanged: _onQuantityChanged,
          ),
        ),
        
        const SizedBox(width: AppThemeExtension.spacingM),
        
        // Total
        Expanded(
          flex: 2,
          child: CurrencyInputField(
            label: 'Total',
            value: widget.item.totalInDollars,
            onChanged: _onTotalChanged,
          ),
        ),
      ],
    );
  }
}
