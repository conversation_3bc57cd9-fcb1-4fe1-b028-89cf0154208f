import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bill_splitter/ui/bill_entry/view_model/bill_entry_view_model.dart';
import 'package:bill_splitter/ui/core/themes/app_theme.dart';
import 'package:bill_splitter/utils/currency_formatter.dart';

/// Widget that displays the bill total
/// 
/// This widget shows the bill total with a breakdown of items and tip
/// when applicable. The display format changes based on whether a tip
/// is present.
class BillTotalWidget extends StatelessWidget {
  /// Creates a BillTotalWidget
  const BillTotalWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        final bill = viewModel.bill;
        final theme = Theme.of(context);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppThemeExtension.spacingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Title
                Text(
                  'Bill Total',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppThemeExtension.spacingM),
                
                // Total breakdown
                if (bill.hasTip) ...[
                  // With tip: show breakdown
                  Text(
                    '${CurrencyFormatter.formatDollarsAsUSD(bill.subtotalInDollars)} + ${CurrencyFormatter.formatDollarsAsUSD(bill.tipAmountInDollars)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppThemeExtension.spacingS),
                ] else ...[
                  // Without tip: show "Total" label
                  Text(
                    'Total',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppThemeExtension.spacingS),
                ],
                
                // Final total amount
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppThemeExtension.spacingM,
                    horizontal: AppThemeExtension.spacingL,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(AppThemeExtension.radiusL),
                  ),
                  child: Text(
                    CurrencyFormatter.formatDollarsAsUSD(bill.totalInDollars),
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                
                // Additional information
                if (bill.itemCount > 0) ...[
                  const SizedBox(height: AppThemeExtension.spacingM),
                  Text(
                    '${bill.itemCount} item${bill.itemCount == 1 ? '' : 's'}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
