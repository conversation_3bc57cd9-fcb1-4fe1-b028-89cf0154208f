import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bill_splitter/domain/models/bill_item.dart';
import 'package:bill_splitter/ui/bill_entry/view_model/bill_entry_view_model.dart';
import 'package:bill_splitter/ui/bill_entry/widgets/bill_item_widget.dart';
import 'package:bill_splitter/ui/core/themes/app_theme.dart';

/// Widget that displays and manages the list of bill items
/// 
/// This widget provides a scrollable list of bill items with the ability
/// to add new items and manage existing ones.
class ItemListWidget extends StatelessWidget {
  /// Creates an ItemListWidget
  const ItemListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        final items = viewModel.bill.items;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Items list
            ...items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: AppThemeExtension.spacingM),
                child: BillItemWidget(
                  item: item,
                  itemIndex: index,
                  canDelete: items.length > 1,
                  onItemUpdated: (updatedItem) {
                    viewModel.updateItemCommand.execute(
                      UpdateItemParams(
                        itemIndex: index,
                        updatedItem: updatedItem,
                      ),
                    );
                  },
                  onDeletePressed: () {
                    viewModel.deleteItemCommand.execute(index);
                  },
                ),
              );
            }).toList(),
            
            // Add item button
            const SizedBox(height: AppThemeExtension.spacingM),
            _AddItemButton(),
          ],
        );
      },
    );
  }
}

/// Button to add a new item to the bill
class _AddItemButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        return OutlinedButton.icon(
          onPressed: viewModel.addItemCommand.canExecute
              ? () => viewModel.addItemCommand.execute()
              : null,
          icon: viewModel.addItemCommand.isExecuting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.add),
          label: const Text('Add Item'),
        );
      },
    );
  }
}
