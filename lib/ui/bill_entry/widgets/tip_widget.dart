import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bill_splitter/ui/bill_entry/view_model/bill_entry_view_model.dart';
import 'package:bill_splitter/ui/core/ui/currency_input_field.dart';
import 'package:bill_splitter/ui/core/ui/integer_input_field.dart';
import 'package:bill_splitter/ui/core/ui/responsive_layout.dart';
import 'package:bill_splitter/ui/core/themes/app_theme.dart';

/// Widget that displays and manages the tip section
/// 
/// This widget allows users to add, edit, or remove a tip from the bill.
/// It provides both percentage and amount input fields with automatic
/// calculation between them.
class TipWidget extends StatelessWidget {
  /// Creates a TipWidget
  const TipWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        final bill = viewModel.bill;
        
        if (!bill.hasTip) {
          return _AddTipButton();
        }
        
        return _TipEditor(tip: bill.tip!);
      },
    );
  }
}

/// Button to add a tip to the bill
class _AddTipButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppThemeExtension.spacingM),
            child: Column(
              children: [
                Text(
                  'Tip',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppThemeExtension.spacingM),
                OutlinedButton.icon(
                  onPressed: viewModel.addTipCommand.canExecute
                      ? () => viewModel.addTipCommand.execute()
                      : null,
                  icon: viewModel.addTipCommand.isExecuting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add),
                  label: const Text('Add Tip'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Widget for editing tip percentage and amount
class _TipEditor extends StatelessWidget {
  const _TipEditor({required this.tip});

  final tip;

  @override
  Widget build(BuildContext context) {
    return Consumer<BillEntryViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppThemeExtension.spacingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and delete button
                Row(
                  children: [
                    Text(
                      'Tip',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: viewModel.deleteTipCommand.canExecute
                          ? () => viewModel.deleteTipCommand.execute()
                          : null,
                      icon: viewModel.deleteTipCommand.isExecuting
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.delete_outline),
                      tooltip: 'Delete Tip',
                    ),
                  ],
                ),
                
                const SizedBox(height: AppThemeExtension.spacingM),
                
                // Input fields
                ResponsiveLayout(
                  mobile: _buildMobileLayout(viewModel),
                  tablet: _buildTabletLayout(viewModel),
                  desktop: _buildDesktopLayout(viewModel),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMobileLayout(BillEntryViewModel viewModel) {
    return Column(
      children: [
        // Percentage field (full width)
        IntegerInputField(
          label: 'Tip Percentage (%)',
          value: tip.percentage,
          min: 0,
          onChanged: (percentage) {
            viewModel.updateTipCommand.execute(
              UpdateTipParams.percentage(percentage),
            );
          },
        ),
        
        const SizedBox(height: AppThemeExtension.spacingM),
        
        // Amount field (full width)
        CurrencyInputField(
          label: 'Tip Amount',
          value: tip.amountInDollars,
          onChanged: (amount) {
            viewModel.updateTipCommand.execute(
              UpdateTipParams.amount(amount),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BillEntryViewModel viewModel) {
    return Row(
      children: [
        // Percentage field
        Expanded(
          child: IntegerInputField(
            label: 'Tip Percentage (%)',
            value: tip.percentage,
            min: 0,
            onChanged: (percentage) {
              viewModel.updateTipCommand.execute(
                UpdateTipParams.percentage(percentage),
              );
            },
          ),
        ),
        
        const SizedBox(width: AppThemeExtension.spacingM),
        
        // Amount field
        Expanded(
          child: CurrencyInputField(
            label: 'Tip Amount',
            value: tip.amountInDollars,
            onChanged: (amount) {
              viewModel.updateTipCommand.execute(
                UpdateTipParams.amount(amount),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BillEntryViewModel viewModel) {
    return Row(
      children: [
        // Percentage field
        Expanded(
          child: IntegerInputField(
            label: 'Tip Percentage (%)',
            value: tip.percentage,
            min: 0,
            onChanged: (percentage) {
              viewModel.updateTipCommand.execute(
                UpdateTipParams.percentage(percentage),
              );
            },
          ),
        ),
        
        const SizedBox(width: AppThemeExtension.spacingM),
        
        // Amount field
        Expanded(
          child: CurrencyInputField(
            label: 'Tip Amount',
            value: tip.amountInDollars,
            onChanged: (amount) {
              viewModel.updateTipCommand.execute(
                UpdateTipParams.amount(amount),
              );
            },
          ),
        ),
      ],
    );
  }
}
