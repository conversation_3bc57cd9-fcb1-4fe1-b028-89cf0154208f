import 'package:flutter/foundation.dart';

/// Base class for implementing the Command pattern
/// 
/// Commands encapsulate user actions and business logic,
/// providing a clean separation between UI and business logic.
abstract class Command<T> extends ChangeNotifier {
  /// Creates a new command
  Command();

  bool _isExecuting = false;
  String? _error;

  /// Whether the command is currently executing
  bool get isExecuting => _isExecuting;

  /// The last error that occurred during execution
  String? get error => _error;

  /// Whether the command has an error
  bool get hasError => _error != null;

  /// Whether the command can be executed
  bool get canExecute => !_isExecuting;

  /// Executes the command with the given parameter
  Future<void> execute([T? parameter]) async {
    if (!canExecute) return;

    _setExecuting(true);
    _clearError();

    try {
      await doExecute(parameter);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setExecuting(false);
    }
  }

  /// Subclasses override this method to implement the command logic
  @protected
  Future<void> doExecute(T? parameter);

  /// Clears any error state
  void clearError() {
    _clearError();
  }

  void _setExecuting(bool value) {
    if (_isExecuting != value) {
      _isExecuting = value;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}

/// A command that doesn't require parameters
abstract class SimpleCommand extends Command<void> {
  /// Executes the command without parameters
  Future<void> call() => execute();
}

/// A command that requires a parameter of type T
abstract class ParameterCommand<T> extends Command<T> {
  /// Executes the command with the given parameter
  Future<void> call(T parameter) => execute(parameter);
}
