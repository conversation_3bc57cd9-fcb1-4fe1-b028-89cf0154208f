import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bill_splitter/utils/currency_formatter.dart';

/// A text input field specifically designed for currency input
/// 
/// This widget provides a consistent interface for entering currency values
/// with proper formatting and validation.
class CurrencyInputField extends StatefulWidget {
  /// Creates a currency input field
  /// 
  /// [label] - The label text for the field
  /// [value] - The initial value in dollars
  /// [onChanged] - Callback when the value changes
  /// [enabled] - Whether the field is enabled for input
  /// [locale] - The locale for currency formatting
  const CurrencyInputField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.locale = 'en_US',
  });

  final String label;
  final double value;
  final ValueChanged<double> onChanged;
  final bool enabled;
  final String locale;

  @override
  State<CurrencyInputField> createState() => _CurrencyInputFieldState();
}

class _CurrencyInputFieldState extends State<CurrencyInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();
    _updateControllerText();

    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _updateControllerText();
      }
    });
  }

  @override
  void didUpdateWidget(CurrencyInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value && !_focusNode.hasFocus) {
      _updateControllerText();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateControllerText() {
    final formattedValue = widget.value.toStringAsFixed(2);
    if (_controller.text != formattedValue) {
      _controller.text = formattedValue;
    }
  }

  void _onTextChanged(String text) {
    final value = CurrencyFormatter.parseCurrencyToDollars(text) ?? 0.0;
    widget.onChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      decoration: InputDecoration(
        labelText: widget.label,
        prefixText: widget.locale.startsWith('pt') ? 'R\$ ' : '\$ ',
      ),
      onChanged: _onTextChanged,
    );
  }
}
