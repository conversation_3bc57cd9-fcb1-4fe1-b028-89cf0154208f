import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A text input field specifically designed for integer input
/// 
/// This widget provides a consistent interface for entering integer values
/// with proper validation.
class IntegerInputField extends StatefulWidget {
  /// Creates an integer input field
  /// 
  /// [label] - The label text for the field
  /// [value] - The initial value
  /// [onChanged] - Callback when the value changes
  /// [enabled] - Whether the field is enabled for input
  /// [min] - Minimum allowed value
  /// [max] - Maximum allowed value
  const IntegerInputField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.min,
    this.max,
  });

  final String label;
  final int value;
  final ValueChanged<int> onChanged;
  final bool enabled;
  final int? min;
  final int? max;

  @override
  State<IntegerInputField> createState() => _IntegerInputFieldState();
}

class _IntegerInputFieldState extends State<IntegerInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();
    _updateControllerText();

    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _updateControllerText();
      }
    });
  }

  @override
  void didUpdateWidget(IntegerInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value && !_focusNode.hasFocus) {
      _updateControllerText();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateControllerText() {
    final valueText = widget.value.toString();
    if (_controller.text != valueText) {
      _controller.text = valueText;
    }
  }

  void _onTextChanged(String text) {
    if (text.isEmpty) {
      widget.onChanged(widget.min ?? 0);
      return;
    }

    final value = int.tryParse(text);
    if (value != null) {
      final clampedValue = _clampValue(value);
      widget.onChanged(clampedValue);
    }
  }

  int _clampValue(int value) {
    if (widget.min != null && value < widget.min!) {
      return widget.min!;
    }
    if (widget.max != null && value > widget.max!) {
      return widget.max!;
    }
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      decoration: InputDecoration(
        labelText: widget.label,
      ),
      onChanged: _onTextChanged,
    );
  }
}
