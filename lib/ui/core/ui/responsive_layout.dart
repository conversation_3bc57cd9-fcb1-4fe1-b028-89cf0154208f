import 'package:flutter/material.dart';

/// Responsive layout breakpoints
class Breakpoints {
  /// Private constructor to prevent instantiation
  Breakpoints._();

  /// Mobile breakpoint (phones)
  static const double mobile = 600;

  /// Tablet breakpoint
  static const double tablet = 1024;

  /// Desktop breakpoint
  static const double desktop = 1440;
}

/// Responsive layout widget that adapts to different screen sizes
/// 
/// This widget provides different layouts for mobile, tablet, and desktop
/// following responsive design principles.
class ResponsiveLayout extends StatelessWidget {
  /// Creates a responsive layout
  /// 
  /// [mobile] - Widget to show on mobile devices
  /// [tablet] - Widget to show on tablet devices (optional, falls back to mobile)
  /// [desktop] - Widget to show on desktop devices (optional, falls back to tablet or mobile)
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= Breakpoints.desktop) {
      return desktop ?? tablet ?? mobile;
    } else if (screenWidth >= Breakpoints.tablet) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}

/// Extension on BuildContext for responsive utilities
extension ResponsiveExtension on BuildContext {
  /// Check if the current screen is mobile
  bool get isMobile => MediaQuery.of(this).size.width < Breakpoints.mobile;

  /// Check if the current screen is tablet
  bool get isTablet {
    final width = MediaQuery.of(this).size.width;
    return width >= Breakpoints.mobile && width < Breakpoints.desktop;
  }

  /// Check if the current screen is desktop
  bool get isDesktop => MediaQuery.of(this).size.width >= Breakpoints.desktop;

  /// Get responsive padding based on screen size
  EdgeInsets get responsivePadding {
    if (isDesktop) {
      return const EdgeInsets.all(32);
    } else if (isTablet) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(16);
    }
  }

  /// Get responsive horizontal padding
  EdgeInsets get responsiveHorizontalPadding {
    if (isDesktop) {
      return const EdgeInsets.symmetric(horizontal: 64);
    } else if (isTablet) {
      return const EdgeInsets.symmetric(horizontal: 32);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16);
    }
  }

  /// Get maximum content width for centered layouts
  double get maxContentWidth {
    if (isDesktop) {
      return 800;
    } else if (isTablet) {
      return 600;
    } else {
      return double.infinity;
    }
  }
}
