import 'package:intl/intl.dart';

/// Utility class for formatting currency values
class CurrencyFormatter {
  /// Private constructor to prevent instantiation
  CurrencyFormatter._();

  /// US Dollar formatter
  static final NumberFormat _usdFormatter = NumberFormat.currency(
    locale: 'en_US',
    symbol: '\$',
    decimalDigits: 2,
  );

  /// Brazilian Real formatter
  static final NumberFormat _brlFormatter = NumberFormat.currency(
    locale: 'pt_BR',
    symbol: 'R\$',
    decimalDigits: 2,
  );

  /// Formats a value in cents as USD currency
  static String formatCentsAsUSD(int cents) {
    final dollars = cents / 100.0;
    return _usdFormatter.format(dollars);
  }

  /// Formats a value in dollars as USD currency
  static String formatDollarsAsUSD(double dollars) {
    return _usdFormatter.format(dollars);
  }

  /// Formats a value in cents as BRL currency
  static String formatCentsAsBRL(int cents) {
    final reais = cents / 100.0;
    return _brlFormatter.format(reais);
  }

  /// Formats a value in reais as BRL currency
  static String formatReaisAsBRL(double reais) {
    return _brlFormatter.format(reais);
  }

  /// Formats currency based on locale
  static String formatCents(int cents, String locale) {
    switch (locale) {
      case 'pt':
      case 'pt_BR':
        return formatCentsAsBRL(cents);
      case 'en':
      case 'en_US':
      default:
        return formatCentsAsUSD(cents);
    }
  }

  /// Formats currency based on locale
  static String formatAmount(double amount, String locale) {
    switch (locale) {
      case 'pt':
      case 'pt_BR':
        return formatReaisAsBRL(amount);
      case 'en':
      case 'en_US':
      default:
        return formatDollarsAsUSD(amount);
    }
  }

  /// Parses a currency string to cents
  /// Returns null if parsing fails
  static int? parseCurrencyToCents(String currencyString) {
    try {
      // Remove currency symbols and whitespace
      final cleanString = currencyString
          .replaceAll(RegExp(r'[^\d.,]'), '')
          .replaceAll(',', '.');
      
      if (cleanString.isEmpty) return 0;
      
      final dollars = double.parse(cleanString);
      return (dollars * 100).round();
    } catch (e) {
      return null;
    }
  }

  /// Parses a currency string to dollars
  /// Returns null if parsing fails
  static double? parseCurrencyToDollars(String currencyString) {
    final cents = parseCurrencyToCents(currencyString);
    return cents != null ? cents / 100.0 : null;
  }
}
